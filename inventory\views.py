from django.shortcuts import render, redirect
from django.db.models import Sum
from django.contrib import messages
from .models import Item

def index(request):
    items = Item.objects.all()

    # Calculate statistics
    total_quantity = items.aggregate(Sum('quantity'))['quantity__sum'] or 0
    total_value = sum(item.price * item.quantity for item in items)

    context = {
        'items': items,
        'total_quantity': total_quantity,
        'total_value': f"{total_value:.2f}",
    }

    return render(request, 'index.html', context)

def add_sample_data(request):
    """Add some sample data for demonstration"""
    sample_items = [
        {'name': 'Laptop Computer', 'price': 999.99, 'quantity': 15},
        {'name': 'Wireless Mouse', 'price': 29.99, 'quantity': 50},
        {'name': 'Mechanical Keyboard', 'price': 149.99, 'quantity': 25},
        {'name': 'USB-C Cable', 'price': 19.99, 'quantity': 100},
        {'name': 'Monitor Stand', 'price': 79.99, 'quantity': 30},
        {'name': 'Webcam HD', 'price': 89.99, 'quantity': 20},
    ]

    for item_data in sample_items:
        Item.objects.get_or_create(
            name=item_data['name'],
            defaults={
                'price': item_data['price'],
                'quantity': item_data['quantity']
            }
        )

    messages.success(request, 'Sample data added successfully!')
    return redirect('home')
