from django.shortcuts import render
from django.db.models import Sum
from .models import Item

def index(request):
    items = Item.objects.all()

    # Calculate statistics
    total_quantity = items.aggregate(Sum('quantity'))['quantity__sum'] or 0
    total_value = sum(item.price * item.quantity for item in items)

    context = {
        'items': items,
        'total_quantity': total_quantity,
        'total_value': f"{total_value:.2f}",
    }

    return render(request, 'index.html', context)
